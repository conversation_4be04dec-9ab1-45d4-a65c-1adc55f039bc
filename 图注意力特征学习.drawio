<mxfile host="app.diagrams.net" modified="2025-01-12T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="图注意力特征学习" id="graph-attention-learning">
    <mxGraphModel dx="1800" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="800" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 异构图输入 -->
        <mxCell id="heterograph_input" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#1F497D;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="60" y="120" width="200" height="160" as="geometry" />
        </mxCell>

        <mxCell id="input_title" value="异构图输入" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="130" y="90" width="80" height="25" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna_nodes" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="140" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="antenna_label" value="天线节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="90" y="150" width="50" height="15" as="geometry" />
        </mxCell>

        <mxCell id="antenna_features" value="A₁, A₂, ..., Aₘ" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="90" y="165" width="140" height="15" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag_nodes" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="210" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="tag_label" value="标签节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="90" y="220" width="50" height="15" as="geometry" />
        </mxCell>

        <mxCell id="tag_features" value="T₁, T₂, ..., Tₙ" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="90" y="235" width="140" height="15" as="geometry" />
        </mxCell>

        <!-- 箭头1 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#2C3E50;strokeWidth=3;fillColor=#2C3E50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="200" as="sourcePoint" />
            <mxPoint x="340" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- GATv2注意力机制模块 -->
        <mxCell id="attention_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="360" y="120" width="320" height="160" as="geometry" />
        </mxCell>

        <mxCell id="attention_title" value="GATv2注意力机制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="480" y="90" width="120" height="25" as="geometry" />
        </mxCell>

        <!-- 特征拼接步骤 -->
        <mxCell id="concat_step" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="140" width="280" height="35" as="geometry" />
        </mxCell>

        <mxCell id="concat_label" value="特征拼接：" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="390" y="150" width="60" height="15" as="geometry" />
        </mxCell>

        <mxCell id="concat_formula" value="[𝐡ᵢ ∥ 𝐡ⱼ ∥ 𝐞ᵢⱼ]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="460" y="150" width="180" height="15" as="geometry" />
        </mxCell>

        <!-- 线性变换步骤 -->
        <mxCell id="linear_step" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="185" width="280" height="35" as="geometry" />
        </mxCell>

        <mxCell id="linear_label" value="线性变换：" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="390" y="195" width="60" height="15" as="geometry" />
        </mxCell>

        <mxCell id="linear_formula" value="𝐖ᵣ · [𝐡ᵢ ∥ 𝐡ⱼ ∥ 𝐞ᵢⱼ]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="460" y="195" width="180" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力权重计算 -->
        <mxCell id="attention_step" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="230" width="280" height="35" as="geometry" />
        </mxCell>

        <mxCell id="attention_label" value="注意力权重：" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="390" y="240" width="70" height="15" as="geometry" />
        </mxCell>

        <mxCell id="attention_formula" value="αᵢⱼ⁽ʳ⁾ = Softmax(LeakyReLU(𝐚ᵣᵀ · 𝐖ᵣ[𝐡ᵢ ∥ 𝐡ⱼ ∥ 𝐞ᵢⱼ]))" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="470" y="240" width="180" height="15" as="geometry" />
        </mxCell>

        <!-- 箭头2 -->
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#2C3E50;strokeWidth=3;fillColor=#2C3E50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="200" as="sourcePoint" />
            <mxPoint x="760" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多头注意力聚合模块 -->
        <mxCell id="multihead_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="780" y="120" width="320" height="160" as="geometry" />
        </mxCell>

        <mxCell id="multihead_title" value="多头注意力聚合" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="900" y="90" width="120" height="25" as="geometry" />
        </mxCell>

        <!-- 多个注意力头展示 -->
        <mxCell id="heads_container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#9673A6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="140" width="280" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head1" value="Head 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F0F0;strokeColor=#9673A6;fontSize=10;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="810" y="150" width="50" height="20" as="geometry" />
        </mxCell>

        <mxCell id="head2" value="Head 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F0F0;strokeColor=#9673A6;fontSize=10;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="870" y="150" width="50" height="20" as="geometry" />
        </mxCell>

        <mxCell id="dots" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="930" y="155" width="20" height="10" as="geometry" />
        </mxCell>

        <mxCell id="head_h" value="Head H" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F0F0;strokeColor=#9673A6;fontSize=10;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="960" y="150" width="50" height="20" as="geometry" />
        </mxCell>

        <!-- 拼接聚合 -->
        <mxCell id="concat_operation" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#9673A6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="195" width="280" height="35" as="geometry" />
        </mxCell>

        <mxCell id="concat_op_label" value="拼接聚合：" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="810" y="205" width="60" height="15" as="geometry" />
        </mxCell>

        <mxCell id="concat_op_formula" value="𝐡ᵢ⁽ˡ⁺¹⁾ = Concat(𝐡ᵢ⁽ˡ⁺¹'¹⁾, 𝐡ᵢ⁽ˡ⁺¹'²⁾, ..., 𝐡ᵢ⁽ˡ⁺¹'ᴴ⁾)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="880" y="205" width="190" height="15" as="geometry" />
        </mxCell>

        <!-- 残差连接 -->
        <mxCell id="residual_connection" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#9673A6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="240" width="280" height="30" as="geometry" />
        </mxCell>

        <mxCell id="residual_label" value="残差连接：" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="810" y="250" width="60" height="10" as="geometry" />
        </mxCell>

        <mxCell id="residual_formula" value="𝐡ᵢ⁽ˡ⁺¹⁾ = 𝐡ᵢ⁽ˡ⁺¹⁾ + 𝐡ᵢ⁽ˡ⁾" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="880" y="250" width="190" height="10" as="geometry" />
        </mxCell>

        <!-- 箭头3 -->
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;strokeColor=#2C3E50;strokeWidth=3;fillColor=#2C3E50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1120" y="200" as="sourcePoint" />
            <mxPoint x="1180" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 节点特征更新模块 -->
        <mxCell id="output_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1200" y="120" width="280" height="160" as="geometry" />
        </mxCell>

        <mxCell id="output_title" value="节点特征更新" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1310" y="90" width="100" height="25" as="geometry" />
        </mxCell>

        <!-- 特征聚合公式 -->
        <mxCell id="aggregation_step" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1220" y="140" width="240" height="40" as="geometry" />
        </mxCell>

        <mxCell id="aggregation_label" value="特征聚合：" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1230" y="150" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="aggregation_formula" value="𝐡ᵢ⁽ˡ⁺¹⁾ = σ(∑ᵣ ∑ⱼ∈𝒩ᵢ⁽ʳ⁾ αᵢⱼ⁽ʳ⁾ 𝐖ᵣ⁽ˡ⁾ 𝐡ⱼ⁽ˡ⁾)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1300" y="155" width="150" height="10" as="geometry" />
        </mxCell>

        <!-- 激活函数 -->
        <mxCell id="activation_step" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1220" y="195" width="240" height="35" as="geometry" />
        </mxCell>

        <mxCell id="activation_label" value="激活函数：" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1230" y="205" width="60" height="15" as="geometry" />
        </mxCell>

        <mxCell id="activation_formula" value="σ = ELU (指数线性单元)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1300" y="205" width="150" height="15" as="geometry" />
        </mxCell>

        <!-- 输出特征 -->
        <mxCell id="output_features" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1220" y="240" width="240" height="30" as="geometry" />
        </mxCell>

        <mxCell id="output_label" value="输出特征：" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1230" y="250" width="60" height="10" as="geometry" />
        </mxCell>

        <mxCell id="output_notation" value="𝐇⁽ˡ⁺¹⁾ ∈ ℝⁿˣᵈ'" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1300" y="250" width="150" height="10" as="geometry" />
        </mxCell>

        <!-- 异构图结构示例 -->
        <mxCell id="graph_example" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#6C757D;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="60" y="320" width="600" height="180" as="geometry" />
        </mxCell>

        <mxCell id="graph_title" value="天线-标签异构图结构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="310" y="290" width="140" height="25" as="geometry" />
        </mxCell>

        <!-- 天线节点层 -->
        <mxCell id="antenna_layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="340" width="560" height="50" as="geometry" />
        </mxCell>

        <mxCell id="antenna_layer_label" value="天线节点层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="90" y="350" width="70" height="15" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna_node1" value="A₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=11;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="180" y="355" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node2" value="A₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=11;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="250" y="355" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node3" value="A₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=11;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="320" y="355" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node4" value="A₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=11;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="390" y="355" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna_dots" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="440" y="365" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node_m" value="Aₘ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=11;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="355" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 标签节点层 -->
        <mxCell id="tag_layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="430" width="560" height="50" as="geometry" />
        </mxCell>

        <mxCell id="tag_layer_label" value="标签节点层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="90" y="440" width="70" height="15" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag_node1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#B85450;fontSize=11;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="180" y="445" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag_node2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#B85450;fontSize=11;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="250" y="445" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag_node3" value="T₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#B85450;fontSize=11;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="320" y="445" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag_node4" value="T₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#B85450;fontSize=11;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="390" y="445" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag_dots" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="440" y="455" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="tag_node_n" value="Tₙ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#B85450;fontSize=11;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="445" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 连接边示例 -->
        <mxCell id="edge1" value="" style="endArrow=none;html=1;strokeColor=#6C757D;strokeWidth=2;" edge="1" parent="1" source="antenna_node1" target="tag_node1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge2" value="" style="endArrow=none;html=1;strokeColor=#6C757D;strokeWidth=2;" edge="1" parent="1" source="antenna_node1" target="tag_node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge3" value="" style="endArrow=none;html=1;strokeColor=#6C757D;strokeWidth=2;" edge="1" parent="1" source="antenna_node2" target="tag_node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge4" value="" style="endArrow=none;html=1;strokeColor=#6C757D;strokeWidth=2;" edge="1" parent="1" source="antenna_node3" target="tag_node3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge5" value="" style="endArrow=none;html=1;strokeColor=#6C757D;strokeWidth=2;" edge="1" parent="1" source="antenna_node4" target="tag_node4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 关系类型说明 -->
        <mxCell id="relation_types" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#6C757D;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="720" y="320" width="200" height="180" as="geometry" />
        </mxCell>

        <mxCell id="relation_title" value="关系类型" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="790" y="290" width="60" height="25" as="geometry" />
        </mxCell>

        <!-- 天线-标签关系 -->
        <mxCell id="relation_at" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#1F497D;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="340" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="relation_at_label" value="天线-标签关系 (A-T)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="750" y="350" width="140" height="10" as="geometry" />
        </mxCell>
        <mxCell id="relation_at_desc" value="RSSI信号强度特征" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="750" y="360" width="140" height="10" as="geometry" />
        </mxCell>

        <!-- 标签-天线关系 -->
        <mxCell id="relation_ta" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="390" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="relation_ta_label" value="标签-天线关系 (T-A)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="750" y="400" width="140" height="10" as="geometry" />
        </mxCell>
        <mxCell id="relation_ta_desc" value="空间距离衰减权重" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="750" y="410" width="140" height="10" as="geometry" />
        </mxCell>

        <!-- 标签-标签关系 -->
        <mxCell id="relation_tt" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="440" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="relation_tt_label" value="标签-标签关系 (T-T)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="750" y="450" width="140" height="10" as="geometry" />
        </mxCell>
        <mxCell id="relation_tt_desc" value="隐式空间拓扑关系" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="750" y="460" width="140" height="10" as="geometry" />
        </mxCell>

        <!-- 主图标题 -->
        <mxCell id="figure_caption" value="图 1. 基于GATv2的异构图注意力特征学习架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#2C3E50;" vertex="1" parent="1">
          <mxGeometry x="600" y="530" width="400" height="30" as="geometry" />
        </mxCell>

        <!-- 创新点标注 -->
        <mxCell id="innovation_note" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1000" y="320" width="200" height="180" as="geometry" />
        </mxCell>

        <mxCell id="innovation_title" value="核心创新" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1070" y="290" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="innovation_1" value="• GATv2改进架构" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="340" width="180" height="15" as="geometry" />
        </mxCell>

        <mxCell id="innovation_2" value="• 异构图建模" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="360" width="180" height="15" as="geometry" />
        </mxCell>

        <mxCell id="innovation_3" value="• 多头注意力聚合" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="380" width="180" height="15" as="geometry" />
        </mxCell>

        <mxCell id="innovation_4" value="• 残差连接机制" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="400" width="180" height="15" as="geometry" />
        </mxCell>

        <mxCell id="innovation_5" value="• 空间先验融合" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="420" width="180" height="15" as="geometry" />
        </mxCell>

        <mxCell id="innovation_6" value="• 自适应权重分配" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="440" width="180" height="15" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
