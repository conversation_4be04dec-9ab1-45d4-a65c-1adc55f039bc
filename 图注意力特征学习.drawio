<mxfile host="app.diagrams.net" modified="2025-01-12T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="图注意力特征学习" id="graph-attention-learning">
    <mxGraphModel dx="1800" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="800" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 异构图输入 -->
        <mxCell id="heterograph_input" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#1F497D;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="60" y="120" width="200" height="160" as="geometry" />
        </mxCell>

        <mxCell id="input_title" value="异构图输入" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="130" y="90" width="80" height="25" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna_nodes" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="140" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="antenna_label" value="天线节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="90" y="150" width="50" height="15" as="geometry" />
        </mxCell>

        <mxCell id="antenna_features" value="A₁, A₂, ..., Aₘ" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="90" y="165" width="140" height="15" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag_nodes" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="210" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="tag_label" value="标签节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="90" y="220" width="50" height="15" as="geometry" />
        </mxCell>

        <mxCell id="tag_features" value="T₁, T₂, ..., Tₙ" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="90" y="235" width="140" height="15" as="geometry" />
        </mxCell>

        <!-- 箭头1 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#2C3E50;strokeWidth=3;fillColor=#2C3E50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="200" as="sourcePoint" />
            <mxPoint x="340" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- GATv2注意力机制模块 -->
        <mxCell id="attention_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="360" y="120" width="320" height="160" as="geometry" />
        </mxCell>

        <mxCell id="attention_title" value="GATv2注意力机制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="480" y="90" width="120" height="25" as="geometry" />
        </mxCell>

        <!-- 特征拼接可视化 -->
        <mxCell id="concat_visual" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="140" width="280" height="35" as="geometry" />
        </mxCell>

        <mxCell id="concat_label" value="特征拼接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="390" y="150" width="50" height="15" as="geometry" />
        </mxCell>

        <!-- 特征块可视化 -->
        <mxCell id="feature_block_i" value="hᵢ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#1F497D;fontSize=10;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="450" y="148" width="30" height="18" as="geometry" />
        </mxCell>

        <mxCell id="concat_symbol1" value="⊕" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="485" y="150" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="feature_block_j" value="hⱼ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#1F497D;fontSize=10;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="505" y="148" width="30" height="18" as="geometry" />
        </mxCell>

        <mxCell id="concat_symbol2" value="⊕" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="540" y="150" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="edge_block" value="eᵢⱼ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F0F0;strokeColor=#666666;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="560" y="148" width="30" height="18" as="geometry" />
        </mxCell>

        <mxCell id="concat_arrow" value="" style="endArrow=classic;html=1;strokeColor=#B7950B;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="157" as="sourcePoint" />
            <mxPoint x="630" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 线性变换可视化 -->
        <mxCell id="transform_visual" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="185" width="280" height="35" as="geometry" />
        </mxCell>

        <mxCell id="transform_label" value="线性变换" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="390" y="195" width="50" height="15" as="geometry" />
        </mxCell>

        <!-- 权重矩阵图标 -->
        <mxCell id="weight_matrix" value="W" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;fontSize=12;fontStyle=1;fontColor=#D79B00;" vertex="1" parent="1">
          <mxGeometry x="450" y="190" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="multiply_symbol" value="×" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="485" y="195" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="concat_result" value="拼接特征" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;fontSize=10;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="510" y="193" width="60" height="18" as="geometry" />
        </mxCell>

        <mxCell id="transform_arrow" value="" style="endArrow=classic;html=1;strokeColor=#B7950B;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="202" as="sourcePoint" />
            <mxPoint x="610" y="202" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 注意力权重可视化 -->
        <mxCell id="attention_visual" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="230" width="280" height="35" as="geometry" />
        </mxCell>

        <mxCell id="attention_weight_label" value="注意力权重" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="390" y="240" width="60" height="15" as="geometry" />
        </mxCell>

        <!-- 激活函数图标 -->
        <mxCell id="activation_icon" value="σ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=12;fontStyle=1;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="460" y="235" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="softmax_label" value="Softmax" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="490" y="240" width="40" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力权重结果 -->
        <mxCell id="attention_weights" value="α₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=10;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="540" y="235" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="attention_weights2" value="α₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=10;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="565" y="235" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="attention_dots" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="590" y="240" width="15" height="10" as="geometry" />
        </mxCell>

        <mxCell id="attention_weights_n" value="αₙ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=10;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="610" y="235" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 箭头2 -->
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#2C3E50;strokeWidth=3;fillColor=#2C3E50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="200" as="sourcePoint" />
            <mxPoint x="760" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多头注意力聚合模块 -->
        <mxCell id="multihead_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="780" y="120" width="320" height="160" as="geometry" />
        </mxCell>

        <mxCell id="multihead_title" value="多头注意力聚合" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="900" y="90" width="120" height="25" as="geometry" />
        </mxCell>

        <!-- 并行注意力头可视化 -->
        <mxCell id="parallel_heads" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#9673A6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="140" width="280" height="45" as="geometry" />
        </mxCell>

        <mxCell id="parallel_label" value="并行计算" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="810" y="148" width="50" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力头通道 -->
        <mxCell id="head_channel1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F0F0;strokeColor=#9673A6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="870" y="150" width="40" height="25" as="geometry" />
        </mxCell>
        <mxCell id="head1_label" value="H₁" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="880" y="157" width="20" height="10" as="geometry" />
        </mxCell>

        <mxCell id="head_channel2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F0F0;strokeColor=#9673A6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="920" y="150" width="40" height="25" as="geometry" />
        </mxCell>
        <mxCell id="head2_label" value="H₂" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="930" y="157" width="20" height="10" as="geometry" />
        </mxCell>

        <mxCell id="head_dots" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="970" y="157" width="15" height="10" as="geometry" />
        </mxCell>

        <mxCell id="head_channel_h" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F0F0;strokeColor=#9673A6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="995" y="150" width="40" height="25" as="geometry" />
        </mxCell>
        <mxCell id="head_h_label" value="Hₕ" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="1005" y="157" width="20" height="10" as="geometry" />
        </mxCell>

        <!-- 聚合箭头 -->
        <mxCell id="aggregation_arrows" value="" style="endArrow=classic;html=1;strokeColor=#9673A6;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="890" y="180" as="sourcePoint" />
            <mxPoint x="940" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="aggregation_arrows2" value="" style="endArrow=classic;html=1;strokeColor=#9673A6;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="940" y="180" as="sourcePoint" />
            <mxPoint x="940" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="aggregation_arrows3" value="" style="endArrow=classic;html=1;strokeColor=#9673A6;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1015" y="180" as="sourcePoint" />
            <mxPoint x="940" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 拼接聚合可视化 -->
        <mxCell id="concat_aggregation" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#9673A6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="195" width="280" height="35" as="geometry" />
        </mxCell>

        <mxCell id="concat_agg_label" value="特征拼接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="810" y="205" width="50" height="15" as="geometry" />
        </mxCell>

        <!-- 拼接符号 -->
        <mxCell id="concat_icon" value="⊕" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;fontSize=16;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="930" y="200" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="concat_result_block" value="融合特征" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="970" y="205" width="60" height="15" as="geometry" />
        </mxCell>

        <!-- 残差连接可视化 -->
        <mxCell id="residual_visual" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#9673A6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="240" width="280" height="30" as="geometry" />
        </mxCell>

        <mxCell id="residual_vis_label" value="残差连接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="810" y="250" width="50" height="10" as="geometry" />
        </mxCell>

        <!-- 残差连接图形 -->
        <mxCell id="input_feature" value="输入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#1F497D;fontSize=9;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="870" y="247" width="35" height="16" as="geometry" />
        </mxCell>

        <mxCell id="plus_symbol" value="+" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;fontSize=12;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="915" y="247" width="16" height="16" as="geometry" />
        </mxCell>

        <mxCell id="processed_feature" value="处理后" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;fontSize=9;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="940" y="247" width="35" height="16" as="geometry" />
        </mxCell>

        <mxCell id="equals_symbol" value="=" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="985" y="250" width="10" height="10" as="geometry" />
        </mxCell>

        <mxCell id="final_feature" value="最终特征" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=9;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1005" y="247" width="50" height="16" as="geometry" />
        </mxCell>

        <!-- 箭头3 -->
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;strokeColor=#2C3E50;strokeWidth=3;fillColor=#2C3E50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1120" y="200" as="sourcePoint" />
            <mxPoint x="1200" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 节点特征更新模块 -->
        <mxCell id="output_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1200" y="120" width="380" height="160" as="geometry" />
        </mxCell>

        <mxCell id="output_title" value="节点特征更新" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1350" y="90" width="100" height="25" as="geometry" />
        </mxCell>

        <!-- 步骤1：邻居特征收集 -->
        <mxCell id="step1_collection" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1220" y="140" width="160" height="60" as="geometry" />
        </mxCell>

        <mxCell id="step1_label" value="步骤1：邻居特征收集" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1230" y="145" width="140" height="12" as="geometry" />
        </mxCell>

        <!-- 异构邻居节点 -->
        <!-- 天线邻居 -->
        <mxCell id="antenna_neighbor1" value="A₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=8;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1230" y="160" width="18" height="18" as="geometry" />
        </mxCell>

        <mxCell id="antenna_neighbor2" value="A₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=8;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1255" y="160" width="18" height="18" as="geometry" />
        </mxCell>

        <!-- 标签邻居 -->
        <mxCell id="tag_neighbor1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=8;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1280" y="160" width="18" height="18" as="geometry" />
        </mxCell>

        <mxCell id="tag_neighbor2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=8;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1305" y="160" width="18" height="18" as="geometry" />
        </mxCell>

        <mxCell id="more_neighbors" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1330" y="164" width="15" height="10" as="geometry" />
        </mxCell>

        <!-- 中心目标节点 -->
        <mxCell id="target_node" value="Tᵢ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFE6CC;strokeColor=#D79B00;fontSize=10;fontStyle=1;fontColor=#D79B00;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="1290" y="180" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 带权重的聚合箭头 -->
        <mxCell id="weighted_arrow1" value="" style="endArrow=classic;html=1;strokeColor=#82B366;strokeWidth=4;" edge="1" parent="1" source="antenna_neighbor1" target="target_node">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="250" as="sourcePoint" />
            <mxPoint x="1450" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="weight_label1" value="α₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=8;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="1250" y="175" width="12" height="12" as="geometry" />
        </mxCell>

        <mxCell id="weighted_arrow2" value="" style="endArrow=classic;html=1;strokeColor=#82B366;strokeWidth=3;" edge="1" parent="1" source="antenna_neighbor2" target="target_node">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="250" as="sourcePoint" />
            <mxPoint x="1450" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="weight_label2" value="α₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=8;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="1270" y="175" width="12" height="12" as="geometry" />
        </mxCell>

        <mxCell id="weighted_arrow3" value="" style="endArrow=classic;html=1;strokeColor=#B85450;strokeWidth=5;" edge="1" parent="1" source="tag_neighbor1" target="target_node">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="250" as="sourcePoint" />
            <mxPoint x="1450" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="weight_label3" value="α₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=8;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="1285" y="175" width="12" height="12" as="geometry" />
        </mxCell>

        <mxCell id="weighted_arrow4" value="" style="endArrow=classic;html=1;strokeColor=#B85450;strokeWidth=2;" edge="1" parent="1" source="tag_neighbor2" target="target_node">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1400" y="250" as="sourcePoint" />
            <mxPoint x="1450" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="weight_label4" value="α₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=8;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="1310" y="175" width="12" height="12" as="geometry" />
        </mxCell>

        <!-- 步骤2：权重加权求和 -->
        <mxCell id="step2_aggregation" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1400" y="140" width="80" height="60" as="geometry" />
        </mxCell>

        <mxCell id="step2_label" value="步骤2：加权求和" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1405" y="145" width="70" height="12" as="geometry" />
        </mxCell>

        <!-- 求和符号 -->
        <mxCell id="sum_symbol" value="Σ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;fontSize=20;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="1425" y="165" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 聚合箭头从步骤1到步骤2 -->
        <mxCell id="step1_to_step2" value="" style="endArrow=classic;html=1;strokeColor=#2E7D32;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1380" y="170" as="sourcePoint" />
            <mxPoint x="1400" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 步骤3：激活函数处理 -->
        <mxCell id="step3_activation" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1500" y="140" width="80" height="60" as="geometry" />
        </mxCell>

        <mxCell id="step3_label" value="步骤3：激活处理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1505" y="145" width="70" height="12" as="geometry" />
        </mxCell>

        <!-- ELU激活函数图形 -->
        <mxCell id="elu_function" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1515" y="165" width="50" height="25" as="geometry" />
        </mxCell>

        <mxCell id="elu_curve_visual" value="σ" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="1530" y="172" width="20" height="10" as="geometry" />
        </mxCell>

        <mxCell id="elu_name" value="ELU" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="1530" y="180" width="20" height="8" as="geometry" />
        </mxCell>

        <!-- 步骤间连接箭头 -->
        <mxCell id="step2_to_step3" value="" style="endArrow=classic;html=1;strokeColor=#2E7D32;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1480" y="170" as="sourcePoint" />
            <mxPoint x="1500" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 步骤4：输出更新特征 -->
        <mxCell id="step4_output" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1220" y="220" width="360" height="50" as="geometry" />
        </mxCell>

        <mxCell id="step4_label" value="步骤4：输出更新特征" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1350" y="225" width="120" height="12" as="geometry" />
        </mxCell>

        <!-- 更新后的节点特征 -->
        <mxCell id="updated_node" value="Tᵢ'" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=12;fontStyle=1;fontColor=#2E7D32;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="1250" y="240" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 特征向量表示 -->
        <mxCell id="feature_vector" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#1F497D;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="1300" y="245" width="120" height="20" as="geometry" />
        </mxCell>

        <mxCell id="vector_elements" value="[h₁', h₂', h₃', ..., hd']" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="1310" y="250" width="100" height="10" as="geometry" />
        </mxCell>

        <!-- 维度标注 -->
        <mxCell id="dimension_info" value="d维特征" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontStyle=2;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1440" y="250" width="40" height="10" as="geometry" />
        </mxCell>

        <!-- 从步骤3到步骤4的箭头 -->
        <mxCell id="step3_to_step4" value="" style="endArrow=classic;html=1;strokeColor=#2E7D32;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1540" y="200" as="sourcePoint" />
            <mxPoint x="1540" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 权重重要性说明 -->
        <mxCell id="weight_importance" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1350" y="160" width="40" height="30" as="geometry" />
        </mxCell>

        <mxCell id="weight_note" value="权重大小" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="1355" y="165" width="30" height="8" as="geometry" />
        </mxCell>

        <mxCell id="arrow_thickness_note" value="↔箭头粗细" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="1355" y="175" width="30" height="8" as="geometry" />
        </mxCell>

        <!-- 异构图结构示例 -->
        <mxCell id="graph_example" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#6C757D;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="60" y="320" width="600" height="200" as="geometry" />
        </mxCell>

        <mxCell id="graph_title" value="天线-标签异构图结构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="310" y="290" width="140" height="25" as="geometry" />
        </mxCell>

        <!-- 天线节点层 -->
        <mxCell id="antenna_layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="340" width="560" height="60" as="geometry" />
        </mxCell>

        <mxCell id="antenna_layer_label" value="天线节点层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="90" y="350" width="70" height="15" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna_node1" value="A₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=11;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="180" y="360" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node2" value="A₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=11;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="250" y="360" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node3" value="A₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=11;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="320" y="360" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node4" value="A₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=11;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="390" y="360" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="antenna_dots" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="445" y="372" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node_m" value="Aₘ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=11;fontColor=#2E7D32;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="485" y="360" width="35" height="35" as="geometry" />
        </mxCell>

        <!-- 标签节点层 -->
        <mxCell id="tag_layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="440" width="560" height="60" as="geometry" />
        </mxCell>

        <mxCell id="tag_layer_label" value="标签节点层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="90" y="450" width="70" height="15" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag_node1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#B85450;fontSize=11;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="180" y="460" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="tag_node2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#B85450;fontSize=11;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="250" y="460" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="tag_node3" value="T₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#B85450;fontSize=11;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="320" y="460" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="tag_node4" value="T₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#B85450;fontSize=11;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="390" y="460" width="35" height="35" as="geometry" />
        </mxCell>
        <mxCell id="tag_dots" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="445" y="472" width="20" height="10" as="geometry" />
        </mxCell>
        <mxCell id="tag_node_n" value="Tₙ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#B85450;fontSize=11;fontColor=#C62828;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="485" y="460" width="35" height="35" as="geometry" />
        </mxCell>

        <!-- 连接边示例 - 使用不同颜色表示不同关系类型 -->
        <!-- A-T关系 -->
        <mxCell id="edge_at1" value="" style="endArrow=none;html=1;strokeColor=#1F497D;strokeWidth=3;" edge="1" parent="1" source="antenna_node1" target="tag_node1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_at2" value="" style="endArrow=none;html=1;strokeColor=#1F497D;strokeWidth=3;" edge="1" parent="1" source="antenna_node1" target="tag_node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_at3" value="" style="endArrow=none;html=1;strokeColor=#1F497D;strokeWidth=3;" edge="1" parent="1" source="antenna_node2" target="tag_node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_at4" value="" style="endArrow=none;html=1;strokeColor=#1F497D;strokeWidth=3;" edge="1" parent="1" source="antenna_node3" target="tag_node3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_at5" value="" style="endArrow=none;html=1;strokeColor=#1F497D;strokeWidth=3;" edge="1" parent="1" source="antenna_node4" target="tag_node4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- T-T关系 (标签间连接) -->
        <mxCell id="edge_tt1" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=2;dashed=1;" edge="1" parent="1" source="tag_node1" target="tag_node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_tt2" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=2;dashed=1;" edge="1" parent="1" source="tag_node2" target="tag_node3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge_tt3" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=2;dashed=1;" edge="1" parent="1" source="tag_node3" target="tag_node4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 注意力权重可视化 -->
        <mxCell id="attention_weight_vis1" value="α₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=8;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="190" y="410" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="attention_weight_vis2" value="α₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=8;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="220" y="410" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="attention_weight_vis3" value="α₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=8;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="260" y="410" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 关系类型图形化说明 -->
        <mxCell id="relation_types" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#6C757D;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="720" y="320" width="200" height="200" as="geometry" />
        </mxCell>

        <mxCell id="relation_title" value="关系类型" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="790" y="290" width="60" height="25" as="geometry" />
        </mxCell>

        <!-- 天线-标签关系图形化 -->
        <mxCell id="relation_at_visual" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#1F497D;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="340" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="relation_at_label" value="天线-标签关系" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="750" y="345" width="80" height="10" as="geometry" />
        </mxCell>

        <!-- A-T关系图标 -->
        <mxCell id="antenna_icon" value="A" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="750" y="360" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="at_connection" value="" style="endArrow=classic;html=1;strokeColor=#1F497D;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="775" y="370" as="sourcePoint" />
            <mxPoint x="805" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="tag_icon_at" value="T" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="810" y="360" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="rssi_label" value="RSSI" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="840" y="365" width="25" height="10" as="geometry" />
        </mxCell>

        <!-- 标签-天线关系图形化 -->
        <mxCell id="relation_ta_visual" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="400" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="relation_ta_label" value="标签-天线关系" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="750" y="405" width="80" height="10" as="geometry" />
        </mxCell>

        <!-- T-A关系图标 -->
        <mxCell id="tag_icon_ta" value="T" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="750" y="420" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="ta_connection" value="" style="endArrow=classic;html=1;strokeColor=#D6B656;strokeWidth=3;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="775" y="430" as="sourcePoint" />
            <mxPoint x="805" y="430" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="antenna_icon_ta" value="A" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="810" y="420" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="distance_label" value="距离" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="840" y="425" width="25" height="10" as="geometry" />
        </mxCell>

        <!-- 标签-标签关系图形化 -->
        <mxCell id="relation_tt_visual" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="460" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="relation_tt_label" value="标签-标签关系" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="750" y="465" width="80" height="10" as="geometry" />
        </mxCell>

        <!-- T-T关系图标 -->
        <mxCell id="tag_icon_tt1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=9;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="750" y="480" width="18" height="18" as="geometry" />
        </mxCell>

        <mxCell id="tt_connection" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="770" y="489" as="sourcePoint" />
            <mxPoint x="810" y="489" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="tag_icon_tt2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=9;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="812" y="480" width="18" height="18" as="geometry" />
        </mxCell>

        <mxCell id="topology_label" value="拓扑" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=8;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="840" y="485" width="25" height="10" as="geometry" />
        </mxCell>

        <!-- 主图标题 -->
        <mxCell id="figure_caption" value="图 1. 基于GATv2的异构图注意力特征学习架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#2C3E50;" vertex="1" parent="1">
          <mxGeometry x="600" y="530" width="400" height="30" as="geometry" />
        </mxCell>

        <!-- 创新点标注 -->
        <mxCell id="innovation_note" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1000" y="320" width="200" height="180" as="geometry" />
        </mxCell>

        <mxCell id="innovation_title" value="核心创新" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1070" y="290" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="innovation_1" value="• GATv2改进架构" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="340" width="180" height="15" as="geometry" />
        </mxCell>

        <mxCell id="innovation_2" value="• 异构图建模" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="360" width="180" height="15" as="geometry" />
        </mxCell>

        <mxCell id="innovation_3" value="• 多头注意力聚合" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="380" width="180" height="15" as="geometry" />
        </mxCell>

        <mxCell id="innovation_4" value="• 残差连接机制" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="400" width="180" height="15" as="geometry" />
        </mxCell>

        <mxCell id="innovation_5" value="• 空间先验融合" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="420" width="180" height="15" as="geometry" />
        </mxCell>

        <mxCell id="innovation_6" value="• 自适应权重分配" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1010" y="440" width="180" height="15" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
